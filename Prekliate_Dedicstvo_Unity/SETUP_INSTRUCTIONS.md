# Inštrukcie na Dokončenie Unity Projektu

## 1. Otvorenie Projektu v Unity

1. **Spustite Unity Hub**
2. **Kliknite na "Open"**
3. **Vyberte priečinok**: `Prekliate_Dedicstvo_Unity`
4. **Unity verzia**: Odporúčame Unity 2021.3 LTS alebo novší

## 2. Vytvorenie UI Štruktúry

### Hlavný Canvas:
```
Hierarchy:
├── Main Camera
├── Canvas (Screen Space - Overlay)
│   ├── MainMenuCanvas
│   │   ├── Background (Image - čierna)
│   │   ├── Title (Text - "Prekliate Dedičstvo")
│   │   ├── NewGameButton (Button)
│   │   ├── ContinueButton (Button)
│   │   ├── ChaptersButton (Button)
│   │   └── SettingsButton (Button)
│   │
│   ├── GameCanvas
│   │   ├── BackgroundImage (Image)
│   │   ├── NarrationPanel
│   │   │   ├── NarrationText (Text)
│   │   │   └── NextButton (Button)
│   │   ├── UIPanel
│   │   │   ├── BackButton (Button)
│   │   │   ├── MainMenuButton (Button)
│   │   │   └── JournalButton (Button)
│   │   │
│   │   ├── ChapterMenuCanvas
│   │   │   ├── ChapterTitle (Text)
│   │   │   ├── ChapterList (Scroll View)
│   │   │   └── BackToMenuButton (Button)
│   │   │
│   │   ├── JournalCanvas
│   │   │   ├── JournalBackground (Image)
│   │   │   ├── JournalText (Text)
│   │   │   ├── PrevPageButton (Button)
│   │   │   ├── NextPageButton (Button)
│   │   │   └── CloseJournalButton (Button)
│   │   │
│   │   └── PuzzleCanvas
│   │       ├── PuzzleBackground (Image)
│   │       ├── SymbolsPanel
│   │       │   ├── CrossButton (Button + Image)
│   │       │   ├── LionButton (Button + Image)
│   │       │   ├── EagleButton (Button + Image)
│   │       │   └── HeartButton (Button + Image)
│   │       ├── SequencePanel
│   │       │   ├── Slot1 (Image)
│   │       │   ├── Slot2 (Image)
│   │       │   ├── Slot3 (Image)
│   │       │   └── Slot4 (Image)
│   │       ├── SubmitButton (Button)
│   │       ├── ResetButton (Button)
│   │       ├── HintButton (Button)
│   │       ├── FeedbackText (Text)
│   │       └── CloseButton (Button)
│   │
│   ├── GameManager (Empty GameObject)
│   ├── AudioManager (Empty GameObject)
│   ├── PuzzleManager (Empty GameObject)
│   ├── ImageLoader (Empty GameObject)
│   ├── UIEffects (Empty GameObject)
│   └── SaveSystem (Empty GameObject)
```

## 3. Nastavenie Canvas

### Canvas Scaler:
- **UI Scale Mode**: Scale With Screen Size
- **Reference Resolution**: 1080 x 1920 (mobilné)
- **Screen Match Mode**: Match Width Or Height
- **Match**: 0.5

### GraphicRaycaster:
- Nechajte predvolené nastavenia

## 4. Pripojenie Skriptov

### GameManager:
1. Vyberte GameObject "GameManager"
2. Pridajte komponent "GameManager" script
3. Pripojte všetky UI referencie v inspektore:
   - mainMenuCanvas → MainMenuCanvas
   - gameCanvas → GameCanvas
   - chapterMenuCanvas → ChapterMenuCanvas
   - journalCanvas → JournalCanvas
   - puzzleCanvas → PuzzleCanvas
   - Všetky tlačidlá a text komponenty

### AudioManager:
1. Vyberte GameObject "AudioManager"
2. Pridajte komponent "AudioManager" script
3. Pridajte 2x AudioSource komponenty (Music a SFX)

### PuzzleManager:
1. Vyberte GameObject "PuzzleManager"
2. Pridajte komponent "PuzzleManager" script
3. Pripojte puzzle UI referencie

### Ostatné Manažéry:
- ImageLoader, UIEffects, SaveSystem - len pridajte skripty

## 5. Vytvorenie Symbolov pre Puzzle

### Potrebné Sprite obrázky:
- `cross.png` - Kríž
- `lion.png` - Lev
- `eagle.png` - Orel
- `heart.png` - Srdce

### Import nastavenia:
- **Texture Type**: Sprite (2D and UI)
- **Sprite Mode**: Single
- **Pixels Per Unit**: 100
- **Filter Mode**: Bilinear
- **Compression**: High Quality

## 6. Nastavenie Tlačidiel

### Všetky Button komponenty:
- **Transition**: Color Tint
- **Normal Color**: Biela
- **Highlighted Color**: Svetlo šedá
- **Pressed Color**: Tmavo šedá
- **Selected Color**: Svetlo modrá
- **Color Multiplier**: 1
- **Fade Duration**: 0.1

### Text komponenty:
- **Font**: Arial (alebo vlastný font)
- **Font Size**: 24-48 (podľa potreby)
- **Color**: Biela
- **Alignment**: Center

## 7. Nastavenie Farieb a Štýlov

### Farebná schéma (podľa preferencií):
- **Pozadie**: Čierna (#000000)
- **Text**: Biela (#FFFFFF)
- **Akcenty**: Fialová (#800080) - podľa preferencií
- **UI Pozadie**: Tmavo šedá (#333333)

### Gotický štýl:
- Použite serif fonty pre lepšiu atmosféru
- Tmavé pozadia s kontrastnými textami
- Jemné tiene a efekty

## 8. Testovanie

### Základné testy:
1. **Spustite hru** (Play button)
2. **Otestujte menu** - všetky tlačidlá
3. **Spustite novú hru** - mal by sa načítať GameCanvas
4. **Otestujte puzzle** - klikanie na symboly
5. **Skúste denník** - navigácia medzi stránkami

### Mobilné testovanie:
1. **Build Settings** → Android/iOS
2. **Player Settings** → nastavte orientáciu na Portrait
3. **Build and Run** na zariadení

## 9. Optimalizácia

### Performance:
- **Texture Compression**: Zapnite pre všetky obrázky
- **Audio Compression**: MP3/OGG pre hudbu, WAV pre SFX
- **UI Optimization**: Použite UI.DefaultControls pre lepšiu performance

### Mobilné optimalizácie:
- **Target Frame Rate**: 60 FPS
- **V-Sync**: Vypnite
- **Multithreaded Rendering**: Zapnite

## 10. Rozšírenia

### Pridanie nových kapitol:
1. Upravte `GameData.cs`
2. Pridajte nové scény do `GameManager.cs`
3. Vytvorte nové obrázky a audio súbory

### Nové puzzle:
1. Rozšírte `PuzzleManager.cs`
2. Vytvorte nové UI komponenty
3. Definujte riešenia

## 11. Build a Deploy

### Android:
```
File → Build Settings
Platform: Android
Texture Compression: ASTC
Architecture: ARM64
```

### iOS:
```
File → Build Settings
Platform: iOS
Target Device: iPhone + iPad
Target OS Version: 11.0
Architecture: ARM64
```

## 12. Riešenie Problémov

### Časté problémy:
- **UI sa nezobrazuje**: Skontrolujte Canvas Scaler
- **Tlačidlá nefungujú**: Skontrolujte EventSystem
- **Audio nehrá**: Skontrolujte AudioListener na kamere
- **Obrázky sa nenačítavaju**: Skontrolujte internetové pripojenie

### Debug:
- Použite `Debug.Log()` pre sledovanie problémov
- Console window pre chybové hlásenia
- Profiler pre performance problémy

---

**Poznámka**: Tento projekt je optimalizovaný pre mobilné zariadenia s portrait orientáciou a slovenským jazykom podľa vašich preferencií.
