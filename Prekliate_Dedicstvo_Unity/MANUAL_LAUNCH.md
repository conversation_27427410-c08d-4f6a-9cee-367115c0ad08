# 🚀 Manuálne Spustenie Unity - Prekliate Dedičstvo

## Ak sa Unity nespúšťa automaticky, postupujte takto:

### 🎯 Metóda 1: Unity Hub (Odporúčané)

1. **Otvorte Unity Hub**
   - Nájdite v Applications alebo Spotlight search
   - Alebo kliknite na ikonu Unity Hub v Dock

2. **Pridajte projekt**
   - Kliknite na **"Open"** alebo **"Add"**
   - Navigujte do priečinka:
     ```
     /Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity
     ```
   - Vyberte tento priečinok a kliknite **"Open"**

3. **Spustite projekt**
   - Projekt sa zobrazí v zozname
   - Kliknite na projekt pre otvorenie

### 🎯 Metóda 2: Drag & Drop

1. **Otvorte Finder**
   - Navigujte do priečinka:
     ```
     /Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/
     ```

2. **Presuňte projekt**
   - Nájdite priečinok **"Prekliate_Dedicstvo_Unity"**
   - Pretiahnite ho na ikonu **Unity Hub** v Dock
   - Alebo pretiahnite na otvorené okno Unity Hub

### 🎯 Metóda 3: Terminál

1. **Otvorte Terminal**
2. **Spustite príkaz**:
   ```bash
   cd "/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity"
   ./launch_unity.sh
   ```

### 🎯 Metóda 4: Priamy spôsob

1. **Otvorte Terminal**
2. **Spustite Unity priamo**:
   ```bash
   "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/MacOS/Unity" -projectPath "/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity"
   ```

## ✅ Po otvorení Unity:

### 1. Počkajte na načítanie
- Unity môže trvať 2-5 minút na prvé otvorenie
- Sledujte progress bar v pravom dolnom rohu
- Počkajte na dokončenie "Importing Assets"

### 2. Otvorte scénu
- V **Project** okne nájdite: `Assets/Scenes/MainScene.unity`
- Double-click na **MainScene.unity**

### 3. Skontrolujte Hierarchy
Mali by ste vidieť:
- **Main Camera**
- **GameManager** (s SimpleGameManager scriptom)
- **AudioManager** (s AudioManager scriptom)

### 4. Testovanie
- Stlačte **Play** ▶️ button
- Testujte klávesové skratky:
  - `T` - Test systémov
  - `M` - Hlavné menu
  - `G` - Nová hra
  - `A` - Menu hudba

### 5. Sledujte Console
- Otvorte **Window > General > Console**
- Mali by ste vidieť debug správy ako:
  ```
  [SimpleGameManager] === TEST HRY ===
  [SimpleGameManager] GameManager: OK
  [SimpleGameManager] AudioManager: OK
  ```

## 🐛 Riešenie Problémov

### Unity sa neotvára:
- **Skontrolujte verziu**: Projekt vyžaduje Unity 2021.3 LTS alebo novší
- **Reštartujte Unity Hub**: Zavrite a znovu otvorte
- **Skontrolujte licenciu**: Unity potrebuje platnú licenciu

### Chyby pri načítavaní:
- **Compiler errors**: Všetky by mali byť opravené
- **Missing references**: Normálne pri prvom otvorení
- **Package errors**: Počkajte na dokončenie Package Manager

### Scéna je prázdna:
- **Otvorte MainScene.unity** z Assets/Scenes/
- **Skontrolujte Hierarchy** - mali by tam byť objekty
- **Ak nie**, použite AutoSetup script

### Skripty nefungujú:
- **Skontrolujte Console** pre chybové hlásenia
- **Recompile scripts**: Assets > Reimport All
- **Restart Unity** ak je potrebné

## 📁 Štruktúra Projektu

```
Prekliate_Dedicstvo_Unity/
├── Assets/
│   ├── Scenes/
│   │   └── MainScene.unity          # ← Otvorte túto scénu
│   └── Scripts/
│       ├── SimpleGameManager.cs     # ← Hlavný script
│       ├── AudioManager.cs
│       └── ... (ostatné skripty)
├── ProjectSettings/
├── README.md
├── QUICK_START.md
├── SETUP_INSTRUCTIONS.md
└── launch_unity.sh                  # ← Spúšťací script
```

## 🎮 Po úspešnom spustení:

1. **Projekt funguje** - môžete pokračovať s vývojom
2. **Vytvorte UI** - podľa SETUP_INSTRUCTIONS.md
3. **Pridajte obsah** - obrázky, zvuky, texty
4. **Testujte na mobile** - Build & Run

## 🆘 Ak nič nefunguje:

1. **Skontrolujte Unity verziu**: Musí byť 2021.3 LTS+
2. **Skúste nový projekt**: File > New Project > 3D
3. **Skopírujte skripty** z Assets/Scripts/ do nového projektu
4. **Kontaktujte podporu** s konkrétnou chybovou správou

---

**Úspešné spustenie! 🎉**
