# 🚀 Rýchly Štart - Prekliate Dedičstvo Unity

## Unity sa už spúšťa! 

Unity Editor sa práve načítava s vaším projektom. Keď sa otvorí, postupujte takto:

## 1. ✅ <PERSON><PERSON><PERSON><PERSON> (2 minúty)

### Keď sa Unity otvorí:
1. **Počkajte** na dokončenie importu skriptov (Progress bar v pravom dolnom rohu)
2. **Otvorte scénu**: `Assets/Scenes/MainScene.unity` (double-click)
3. **Skontrolujte Hierarchy**: Mali by ste vidieť:
   - Main Camera
   - GameManager
   - AudioManager

## 2. 🎮 Okamžité Testovanie

### Spustenie hry:
1. **Stlačte Play button** ▶️ v Unity editore
2. **Klávesové skratky** pre testovanie:
   - `T` - Spustiť test systémov
   - `M` - Zobraziť hlavné menu  
   - `G` - Spustiť novú hru
   - `A` - Spustiť menu hudbu

### Ak sa nič nezobrazuje:
1. Kliknite na **GameManager** v Hierarchy
2. V Inspector-e skontrolujte, či sú pripojené Canvas referencie
3. Ak nie, pokračujte na krok 3

## 3. 🔧 Rýchle Nastavenie UI (5 minút)

### Vytvorenie Canvas:
```
Hierarchy → Right Click → UI → Canvas
```

### Nastavenie Canvas Scaler:
1. Vyberte **Canvas**
2. V **Canvas Scaler** komponente:
   - UI Scale Mode: **Scale With Screen Size**
   - Reference Resolution: **1080 x 1920**
   - Screen Match Mode: **Match Width Or Height**
   - Match: **0.5**

### Vytvorenie Main Menu:
```
Canvas → Right Click → Create Empty → Rename to "MainMenuCanvas"
MainMenuCanvas → Right Click → UI → Image (Background)
MainMenuCanvas → Right Click → UI → Text (Title: "Prekliate Dedičstvo")
MainMenuCanvas → Right Click → UI → Button (Nová Hra)
MainMenuCanvas → Right Click → UI → Button (Pokračovať)
```

## 4. 🔗 Pripojenie Skriptov

### GameManager nastavenie:
1. Vyberte **GameManager** v Hierarchy
2. V Inspector-e nájdite **Game Manager (Script)**
3. Pripojte referencie:
   - **Main Menu Canvas** → MainMenuCanvas
   - **New Game Button** → Button "Nová Hra"
   - **Continue Button** → Button "Pokračovať"

### AudioManager nastavenie:
1. Vyberte **AudioManager**
2. Skontrolujte, či má 2x **Audio Source** komponenty
3. Ak nie, pridajte ich: **Add Component → Audio Source**

## 5. 🎵 Test Audio Systému

### Spustenie hudby:
1. **Play** ▶️ hru
2. Stlačte `A` pre menu hudbu
3. Hudba sa načíta z URL (potrebné internetové pripojenie)

## 6. 🧩 Test Puzzle Systému

### Ak chcete otestovať puzzle:
1. Vytvorte **PuzzleCanvas** (podobne ako MainMenuCanvas)
2. Pridajte 4 tlačidlá pre symboly (Cross, Lion, Eagle, Heart)
3. Pripojte **PuzzleManager** script

## 7. 📱 Mobilné Testovanie

### Build pre Android:
```
File → Build Settings
Platform: Android
Switch Platform
Player Settings → Orientation: Portrait
Build and Run
```

## 8. 🐛 Riešenie Problémov

### Časté problémy:
- **Nič sa nezobrazuje**: Skontrolujte Canvas a EventSystem
- **Tlačidlá nefungujú**: Pridajte EventSystem (UI → Event System)
- **Skripty sa nekompilujú**: Skontrolujte Console pre chyby
- **Audio nehrá**: Skontrolujte internetové pripojenie

### Debug informácie:
- **Console** (Window → General → Console) - chybové hlásenia
- **Inspector** - nastavenia komponentov
- **Scene view** - vizuálne usporiadanie objektov

## 9. 🎯 Ďalšie Kroky

### Po základnom nastavení:
1. **Dokončite UI** podľa `SETUP_INSTRUCTIONS.md`
2. **Pridajte obrázky** pre symboly puzzle
3. **Otestujte všetky funkcie**
4. **Vytvorte build** pre mobilné zariadenie

## 10. 🆘 Pomoc

### Ak potrebujete pomoc:
- Skontrolujte **README.md** pre detailný popis
- Pozrite **SETUP_INSTRUCTIONS.md** pre kompletné inštrukcie
- Použite **QuickTest** script pre diagnostiku

---

## 🎮 Klávesové Skratky

- `T` - Test systémov
- `M` - Hlavné menu
- `G` - Nová hra  
- `A` - Menu hudba
- `Space` - Play/Pause v editore
- `F` - Focus na vybraný objekt

**Úspešné testovanie! 🎉**
