using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using System;

public class UIEffects : MonoBehaviour
{
    public static UIEffects Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    public void TypewriterEffect(Text textComponent, string fullText, float typingSpeed = 0.05f, Action onComplete = null)
    {
        if (textComponent == null) return;

        StartCoroutine(TypewriterCoroutine(textComponent, fullText, typingSpeed, onComplete));
    }

    IEnumerator TypewriterCoroutine(Text textComponent, string fullText, float typingSpeed, Action onComplete)
    {
        textComponent.text = "";
        
        for (int i = 0; i <= fullText.Length; i++)
        {
            textComponent.text = fullText.Substring(0, i);
            yield return new WaitForSeconds(typingSpeed);
        }
        
        if (onComplete != null)
            onComplete.Invoke();
    }

    public void FadeIn(CanvasGroup canvasGroup, float duration = 1f, Action onComplete = null)
    {
        if (canvasGroup == null) return;

        StartCoroutine(FadeCoroutine(canvasGroup, 0f, 1f, duration, onComplete));
    }

    public void FadeOut(CanvasGroup canvasGroup, float duration = 1f, Action onComplete = null)
    {
        if (canvasGroup == null) return;

        StartCoroutine(FadeCoroutine(canvasGroup, 1f, 0f, duration, onComplete));
    }

    IEnumerator FadeCoroutine(CanvasGroup canvasGroup, float startAlpha, float endAlpha, float duration, Action onComplete)
    {
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, elapsed / duration);
            yield return null;
        }
        
        canvasGroup.alpha = endAlpha;
        if (onComplete != null)
            onComplete.Invoke();
    }
    
    public void ScaleButton(Transform buttonTransform, float scaleFactor = 1.1f, float duration = 0.1f)
    {
        if (buttonTransform == null) return;
        
        StartCoroutine(ScaleButtonCoroutine(buttonTransform, scaleFactor, duration));
    }
    
    IEnumerator ScaleButtonCoroutine(Transform buttonTransform, float scaleFactor, float duration)
    {
        Vector3 originalScale = buttonTransform.localScale;
        Vector3 targetScale = originalScale * scaleFactor;
        
        // Scale up
        float elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            buttonTransform.localScale = Vector3.Lerp(originalScale, targetScale, elapsed / duration);
            yield return null;
        }
        
        // Scale down
        elapsed = 0f;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            buttonTransform.localScale = Vector3.Lerp(targetScale, originalScale, elapsed / duration);
            yield return null;
        }
        
        buttonTransform.localScale = originalScale;
    }
    
    public void ShakeTransform(Transform target, float intensity = 10f, float duration = 0.5f)
    {
        if (target == null) return;
        
        StartCoroutine(ShakeCoroutine(target, intensity, duration));
    }
    
    IEnumerator ShakeCoroutine(Transform target, float intensity, float duration)
    {
        Vector3 originalPosition = target.localPosition;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            
            float x = Random.Range(-intensity, intensity);
            float y = Random.Range(-intensity, intensity);
            
            target.localPosition = originalPosition + new Vector3(x, y, 0);
            
            yield return null;
        }
        
        target.localPosition = originalPosition;
    }
    
    public void PulseImage(Image image, Color startColor, Color endColor, float duration = 1f, bool loop = false)
    {
        if (image == null) return;
        
        StartCoroutine(PulseCoroutine(image, startColor, endColor, duration, loop));
    }
    
    IEnumerator PulseCoroutine(Image image, Color startColor, Color endColor, float duration, bool loop)
    {
        do
        {
            // Fade to end color
            float elapsed = 0f;
            while (elapsed < duration / 2)
            {
                elapsed += Time.deltaTime;
                image.color = Color.Lerp(startColor, endColor, elapsed / (duration / 2));
                yield return null;
            }
            
            // Fade back to start color
            elapsed = 0f;
            while (elapsed < duration / 2)
            {
                elapsed += Time.deltaTime;
                image.color = Color.Lerp(endColor, startColor, elapsed / (duration / 2));
                yield return null;
            }
            
        } while (loop);
    }
    
    public void SlideIn(RectTransform rectTransform, Vector2 startPosition, Vector2 endPosition, float duration = 0.5f, Action onComplete = null)
    {
        if (rectTransform == null) return;

        StartCoroutine(SlideCoroutine(rectTransform, startPosition, endPosition, duration, onComplete));
    }

    IEnumerator SlideCoroutine(RectTransform rectTransform, Vector2 startPosition, Vector2 endPosition, float duration, Action onComplete)
    {
        rectTransform.anchoredPosition = startPosition;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            rectTransform.anchoredPosition = Vector2.Lerp(startPosition, endPosition, elapsed / duration);
            yield return null;
        }
        
        rectTransform.anchoredPosition = endPosition;
        if (onComplete != null)
            onComplete.Invoke();
    }
}
