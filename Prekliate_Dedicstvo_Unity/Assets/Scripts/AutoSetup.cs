using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using UnityEditor;

[InitializeOnLoad]
public class AutoSetup
{
    static AutoSetup()
    {
        EditorApplication.delayCall += SetupScene;
    }
    
    static void SetupScene()
    {
        // Skontrolovať, či už existuje Canvas
        Canvas existingCanvas = Object.FindObjectOfType<Canvas>();
        if (existingCanvas != null)
        {
            Debug.Log("Canvas už existuje, preskakujem automatické nastavenie.");
            return;
        }
        
        Debug.Log("Spúšťam automatické nastavenie scény...");
        
        // Vytvoriť EventSystem ak neexistuje
        if (Object.FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystem = new GameObject("EventSystem");
            eventSystem.AddComponent<EventSystem>();
            eventSystem.AddComponent<StandaloneInputModule>();
            Debug.Log("EventSystem vytvorený.");
        }
        
        // Vytvoriť hlavný Canvas
        GameObject canvasGO = new GameObject("Canvas");
        Canvas canvas = canvasGO.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 0;
        
        // Pridať CanvasScaler pre mobilné zariadenia
        CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1080, 1920); // Mobilné rozlíšenie
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 0.5f;
        
        // Pridať GraphicRaycaster
        canvasGO.AddComponent<GraphicRaycaster>();
        
        // Vytvoriť Main Menu Canvas
        GameObject mainMenuCanvas = CreateMainMenuCanvas(canvas.transform);
        
        // Vytvoriť Game Canvas
        GameObject gameCanvas = CreateGameCanvas(canvas.transform);
        
        // Nastaviť referencie v GameManager
        GameManager gameManager = Object.FindObjectOfType<GameManager>();
        if (gameManager != null)
        {
            // Manuálne nastavenie referencií - budú sa nastavovať v editore
            Debug.Log("GameManager nájdený. Referencie nastavte manuálne v editore.");
        }
        
        Debug.Log("Automatické nastavenie scény dokončené!");
    }
    
    static GameObject CreateMainMenuCanvas(Transform parent)
    {
        GameObject mainMenu = new GameObject("MainMenuCanvas");
        mainMenu.transform.SetParent(parent);
        
        Canvas canvas = mainMenu.AddComponent<Canvas>();
        canvas.overrideSorting = true;
        canvas.sortingOrder = 1;
        
        // Pozadie
        GameObject background = new GameObject("Background");
        background.transform.SetParent(mainMenu.transform);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = Color.black;
        
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;
        
        // Titulok
        GameObject title = new GameObject("Title");
        title.transform.SetParent(mainMenu.transform);
        Text titleText = title.AddComponent<Text>();
        titleText.text = "Prekliate Dedičstvo";
        titleText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        titleText.fontSize = 48;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = title.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0.1f, 0.7f);
        titleRect.anchorMax = new Vector2(0.9f, 0.9f);
        titleRect.offsetMin = Vector2.zero;
        titleRect.offsetMax = Vector2.zero;
        
        // Tlačidlá
        CreateMenuButton(mainMenu.transform, "Nová Hra", new Vector2(0.2f, 0.5f), new Vector2(0.8f, 0.6f));
        CreateMenuButton(mainMenu.transform, "Pokračovať", new Vector2(0.2f, 0.4f), new Vector2(0.8f, 0.5f));
        CreateMenuButton(mainMenu.transform, "Kapitoly", new Vector2(0.2f, 0.3f), new Vector2(0.8f, 0.4f));
        CreateMenuButton(mainMenu.transform, "Nastavenia", new Vector2(0.2f, 0.2f), new Vector2(0.8f, 0.3f));
        
        return mainMenu;
    }
    
    static GameObject CreateGameCanvas(Transform parent)
    {
        GameObject gameCanvas = new GameObject("GameCanvas");
        gameCanvas.transform.SetParent(parent);
        
        Canvas canvas = gameCanvas.AddComponent<Canvas>();
        canvas.overrideSorting = true;
        canvas.sortingOrder = 2;
        gameCanvas.SetActive(false); // Začať neaktívny
        
        // Pozadie pre hru
        GameObject background = new GameObject("BackgroundImage");
        background.transform.SetParent(gameCanvas.transform);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = Color.gray;
        
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;
        
        // Narácia panel
        GameObject narrationPanel = new GameObject("NarrationPanel");
        narrationPanel.transform.SetParent(gameCanvas.transform);
        Image panelImage = narrationPanel.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        
        RectTransform panelRect = narrationPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.05f, 0.1f);
        panelRect.anchorMax = new Vector2(0.95f, 0.4f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Narácia text
        GameObject narrationText = new GameObject("NarrationText");
        narrationText.transform.SetParent(narrationPanel.transform);
        Text textComponent = narrationText.AddComponent<Text>();
        textComponent.text = "Vitajte v hre Prekliate Dedičstvo...";
        textComponent.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComponent.fontSize = 24;
        textComponent.color = Color.white;
        textComponent.alignment = TextAnchor.MiddleLeft;
        
        RectTransform textRect = narrationText.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0.05f, 0.05f);
        textRect.anchorMax = new Vector2(0.95f, 0.95f);
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        return gameCanvas;
    }
    
    static void CreateMenuButton(Transform parent, string text, Vector2 anchorMin, Vector2 anchorMax)
    {
        GameObject button = new GameObject(text + "Button");
        button.transform.SetParent(parent);
        
        Image buttonImage = button.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
        
        Button buttonComponent = button.AddComponent<Button>();
        
        RectTransform buttonRect = button.GetComponent<RectTransform>();
        buttonRect.anchorMin = anchorMin;
        buttonRect.anchorMax = anchorMax;
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        // Text tlačidla
        GameObject buttonText = new GameObject("Text");
        buttonText.transform.SetParent(button.transform);
        Text textComponent = buttonText.AddComponent<Text>();
        textComponent.text = text;
        textComponent.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComponent.fontSize = 28;
        textComponent.color = Color.white;
        textComponent.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = buttonText.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }
}
#endif
