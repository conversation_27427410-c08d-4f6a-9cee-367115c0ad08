using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using System;

public class GameManager : MonoBehaviour
{
    [Header("UI References")]
    public Canvas mainMenuCanvas;
    public Canvas gameCanvas;
    public Canvas chapterMenuCanvas;
    public Canvas journalCanvas;
    public Canvas puzzleCanvas;
    
    [Header("Main Menu")]
    public Button newGameButton;
    public Button continueButton;
    public Button chaptersButton;
    public Button settingsButton;
    
    [Header("Game UI")]
    public Image backgroundImage;
    public Text narrationText;
    public Button nextButton;
    public Button backButton;
    public Button mainMenuButton;
    public Button journalButton;
    
    [Header("Chapter Menu")]
    public Transform chapterListParent;
    public GameObject chapterButtonPrefab;
    
    [Header("Journal")]
    public Text journalText;
    public Button journalCloseButton;
    public Button journalPrevButton;
    public Button journalNextButton;
    
    [Head<PERSON>("Puzzle")]
    public Transform puzzleParent;
    public Button puzzleCloseButton;
    public Button puzzleSubmitButton;
    public Button puzzleHintButton;
    
    [Header("Game Data")]
    public GameData gameData;
    
    // Game state
    private bool gameStarted = false;
    private List<string> sceneHistory = new List<string>();
    private List<JournalEntry> journalEntries = new List<JournalEntry>();
    private int currentJournalPage = 0;
    private string currentChapter = "";
    private string currentScene = "";
    
    // Puzzle state
    private List<string> currentPuzzleSequence = new List<string>();
    private string[] currentPuzzleSolution;
    
    public static GameManager Instance { get; private set; }
    
    [System.Serializable]
    public class JournalEntry
    {
        public string title;
        public string content;
        public string timestamp;
        
        public JournalEntry(string title, string content)
        {
            this.title = title;
            this.content = content;
            this.timestamp = System.DateTime.Now.ToString("HH:mm");
        }
    }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        InitializeUI();
        ShowMainMenu();
        
        // Spustenie menu hudby
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayMenuMusic();
        }
    }
    
    void InitializeUI()
    {
        // Main Menu buttons
        if (newGameButton != null)
            newGameButton.onClick.AddListener(StartNewGame);
        if (continueButton != null)
            continueButton.onClick.AddListener(ContinueGame);
        if (chaptersButton != null)
            chaptersButton.onClick.AddListener(ShowChapterMenu);
        if (settingsButton != null)
            settingsButton.onClick.AddListener(ShowSettings);
            
        // Game UI buttons
        if (nextButton != null)
            nextButton.onClick.AddListener(NextScene);
        if (backButton != null)
            backButton.onClick.AddListener(GoBack);
        if (mainMenuButton != null)
            mainMenuButton.onClick.AddListener(GoBackToMenu);
        if (journalButton != null)
            journalButton.onClick.AddListener(ShowJournal);
            
        // Journal buttons
        if (journalCloseButton != null)
            journalCloseButton.onClick.AddListener(CloseJournal);
        if (journalPrevButton != null)
            journalPrevButton.onClick.AddListener(PreviousJournalPage);
        if (journalNextButton != null)
            journalNextButton.onClick.AddListener(NextJournalPage);
            
        // Puzzle buttons
        if (puzzleCloseButton != null)
            puzzleCloseButton.onClick.AddListener(ClosePuzzle);
        if (puzzleSubmitButton != null)
            puzzleSubmitButton.onClick.AddListener(SubmitPuzzle);
        if (puzzleHintButton != null)
            puzzleHintButton.onClick.AddListener(ShowPuzzleHint);
    }
    
    public void ShowMainMenu()
    {
        SetActiveCanvas(mainMenuCanvas);
        gameStarted = false;
        
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayMenuMusic();
        }
    }
    
    public void StartNewGame()
    {
        gameStarted = true;
        sceneHistory.Clear();
        journalEntries.Clear();
        currentJournalPage = 0;
        
        // Začať s kapitolou 2
        LoadChapter("chapter2");
        
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayInGameMusic();
        }
    }
    
    public void ContinueGame()
    {
        // TODO: Implementovať načítanie uloženej hry
        StartNewGame();
    }
    
    public void ShowChapterMenu()
    {
        SetActiveCanvas(chapterMenuCanvas);
        PopulateChapterMenu();
    }
    
    void PopulateChapterMenu()
    {
        if (chapterListParent == null || chapterButtonPrefab == null || gameData == null) return;
        
        // Vymazať existujúce tlačidlá
        foreach (Transform child in chapterListParent)
        {
            Destroy(child.gameObject);
        }
        
        // Vytvoriť tlačidlá pre kapitoly
        foreach (var chapter in gameData.chapters)
        {
            GameObject buttonObj = Instantiate(chapterButtonPrefab, chapterListParent);
            Button button = buttonObj.GetComponent<Button>();
            Text buttonText = buttonObj.GetComponentInChildren<Text>();
            
            if (buttonText != null)
            {
                buttonText.text = chapter.title;
            }
            
            if (button != null)
            {
                string chapterId = chapter.id;
                button.onClick.AddListener(() => LoadChapter(chapterId));
            }
        }
    }
    
    public void LoadChapter(string chapterId)
    {
        currentChapter = chapterId;
        SetActiveCanvas(gameCanvas);
        
        // Načítať prvú scénu kapitoly
        if (chapterId == "chapter2")
        {
            LoadScene("Brana_Zamku");
        }
    }
    
    public void LoadScene(string sceneName)
    {
        currentScene = sceneName;
        sceneHistory.Add(sceneName);
        
        // TODO: Načítať obrázok scény
        // TODO: Spustiť akcie scény
        
        if (sceneName == "Brana_Zamku")
        {
            StartCoroutine(ShowBranaZamkuScene());
        }
    }
    
    IEnumerator ShowBranaZamkuScene()
    {
        yield return new WaitForSeconds(1.5f);

        string narration = "ROZPRÁVAČ:\nKonečne! Cez koruny stromov sa črtajú obrysy mohutných veží. Zámok Van Helsinga sa týči pred vami ako čierna silueta proti búrlivej oblohe. Táto stavba z 13. storočia v dnešnú noc pripomína skôr hrobku dávno mŕtvych kráľov než domov živého človeka.";

        AddJournalEntry("Príchod k bráne", "Konečne som tu. Predo mnou sa týči zámok, ešte hrozivejší ako v mojich predstavách. Brána je pevne zavretá a zdobená podivnými erbmi.");

        ShowNarration(narration, ShowGatePuzzle);
    }
    
    public void ShowNarration(string text, Action onComplete = null)
    {
        if (narrationText != null)
        {
            narrationText.text = text;
        }

        // TODO: Implementovať typing efekt

        if (onComplete != null)
        {
            StartCoroutine(WaitAndExecute(3f, onComplete));
        }
    }

    IEnumerator WaitAndExecute(float delay, Action action)
    {
        yield return new WaitForSeconds(delay);
        if (action != null)
        {
            action.Invoke();
        }
    }
    
    public void AddJournalEntry(string title, string content)
    {
        journalEntries.Add(new JournalEntry(title, content));
    }
    
    public void ShowJournal()
    {
        SetActiveCanvas(journalCanvas);
        UpdateJournalDisplay();
    }
    
    void UpdateJournalDisplay()
    {
        if (journalText == null || journalEntries.Count == 0) return;
        
        if (currentJournalPage >= 0 && currentJournalPage < journalEntries.Count)
        {
            var entry = journalEntries[currentJournalPage];
            journalText.text = $"<b>{entry.title}</b>\n{entry.timestamp}\n\n{entry.content}";
        }
        
        // Aktualizovať tlačidlá navigácie
        if (journalPrevButton != null)
            journalPrevButton.interactable = currentJournalPage > 0;
        if (journalNextButton != null)
            journalNextButton.interactable = currentJournalPage < journalEntries.Count - 1;
    }
    
    public void CloseJournal()
    {
        SetActiveCanvas(gameCanvas);
    }
    
    public void PreviousJournalPage()
    {
        if (currentJournalPage > 0)
        {
            currentJournalPage--;
            UpdateJournalDisplay();
        }
    }
    
    public void NextJournalPage()
    {
        if (currentJournalPage < journalEntries.Count - 1)
        {
            currentJournalPage++;
            UpdateJournalDisplay();
        }
    }
    
    public void ShowGatePuzzle()
    {
        SetActiveCanvas(puzzleCanvas);
        currentPuzzleSolution = new string[] { "cross", "lion", "eagle", "heart" };
        currentPuzzleSequence.Clear();
        
        // TODO: Nastaviť puzzle UI
    }
    
    public void ClosePuzzle()
    {
        SetActiveCanvas(gameCanvas);
    }
    
    public void SubmitPuzzle()
    {
        // TODO: Skontrolovať riešenie puzzle
    }
    
    public void ShowPuzzleHint()
    {
        // TODO: Zobraziť nápovedu
    }
    
    public void NextScene()
    {
        // TODO: Implementovať prechod na ďalšiu scénu
    }
    
    public void GoBack()
    {
        if (sceneHistory.Count > 1)
        {
            sceneHistory.RemoveAt(sceneHistory.Count - 1);
            string previousScene = sceneHistory[sceneHistory.Count - 1];
            LoadScene(previousScene);
        }
    }
    
    public void GoBackToMenu()
    {
        ShowMainMenu();
    }
    
    public void ShowSettings()
    {
        // TODO: Implementovať nastavenia
    }
    
    void SetActiveCanvas(Canvas canvas)
    {
        mainMenuCanvas?.gameObject.SetActive(canvas == mainMenuCanvas);
        gameCanvas?.gameObject.SetActive(canvas == gameCanvas);
        chapterMenuCanvas?.gameObject.SetActive(canvas == chapterMenuCanvas);
        journalCanvas?.gameObject.SetActive(canvas == journalCanvas);
        puzzleCanvas?.gameObject.SetActive(canvas == puzzleCanvas);
    }
}
