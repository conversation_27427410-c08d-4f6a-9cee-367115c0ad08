using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class SimpleGameManager : MonoBehaviour
{
    [Header("UI References")]
    public Canvas mainMenuCanvas;
    public Canvas gameCanvas;
    
    [Header("Main Menu")]
    public Button newGameButton;
    public Button continueButton;
    public Button chaptersButton;
    public Button settingsButton;
    
    [<PERSON><PERSON>("Game UI")]
    public Image backgroundImage;
    public Text narrationText;
    public Button nextButton;
    public Button backButton;
    public Button mainMenuButton;
    
    // Game state
    private bool gameStarted = false;
    private List<string> journalEntries = new List<string>();
    
    public static SimpleGameManager Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        InitializeUI();
        ShowMainMenu();
        
        // Spustenie menu hudby
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayMenuMusic();
        }
    }
    
    void InitializeUI()
    {
        // Main Menu buttons
        if (newGameButton != null)
            newGameButton.onClick.AddListener(StartNewGame);
        if (continueButton != null)
            continueButton.onClick.AddListener(ContinueGame);
        if (chaptersButton != null)
            chaptersButton.onClick.AddListener(ShowChapterMenu);
        if (settingsButton != null)
            settingsButton.onClick.AddListener(ShowSettings);
            
        // Game UI buttons
        if (nextButton != null)
            nextButton.onClick.AddListener(NextScene);
        if (backButton != null)
            backButton.onClick.AddListener(GoBack);
        if (mainMenuButton != null)
            mainMenuButton.onClick.AddListener(GoBackToMenu);
    }
    
    public void ShowMainMenu()
    {
        SetActiveCanvas(mainMenuCanvas);
        gameStarted = false;
        
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayMenuMusic();
        }
        
        Debug.Log("Zobrazené hlavné menu");
    }
    
    public void StartNewGame()
    {
        gameStarted = true;
        journalEntries.Clear();
        
        SetActiveCanvas(gameCanvas);
        
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.PlayInGameMusic();
        }
        
        // Začať s prvou scénou
        StartCoroutine(ShowFirstScene());
        
        Debug.Log("Spustená nová hra");
    }
    
    IEnumerator ShowFirstScene()
    {
        yield return new WaitForSeconds(1f);
        
        string narration = "Vitajte v hre Prekliate Dedičstvo!\n\nOcitáte sa pred záhadným zámkom Van Helsinga. Vaša úloha je odhaliť jeho tajomstvá a vyriešiť puzzle, ktoré vás čakajú.";
        
        ShowNarration(narration);
        
        AddJournalEntry("Začiatok dobrodružstva");
    }
    
    public void ContinueGame()
    {
        // TODO: Implementovať načítanie uloženej hry
        StartNewGame();
        Debug.Log("Pokračovanie v hre");
    }
    
    public void ShowChapterMenu()
    {
        Debug.Log("Zobrazenie menu kapitol");
        // TODO: Implementovať menu kapitol
    }
    
    public void ShowSettings()
    {
        Debug.Log("Zobrazenie nastavení");
        // TODO: Implementovať nastavenia
    }
    
    public void ShowNarration(string text)
    {
        if (narrationText != null)
        {
            narrationText.text = text;
        }
        
        Debug.Log("Narácia: " + text);
    }
    
    public void AddJournalEntry(string entry)
    {
        journalEntries.Add(entry);
        Debug.Log("Pridaný záznam do denníka: " + entry);
    }
    
    public void NextScene()
    {
        Debug.Log("Prechod na ďalšiu scénu");
        // TODO: Implementovať prechod na ďalšiu scénu
    }
    
    public void GoBack()
    {
        Debug.Log("Návrat späť");
        // TODO: Implementovať návrat
    }
    
    public void GoBackToMenu()
    {
        ShowMainMenu();
        Debug.Log("Návrat do hlavného menu");
    }
    
    void SetActiveCanvas(Canvas canvas)
    {
        if (mainMenuCanvas != null)
            mainMenuCanvas.gameObject.SetActive(canvas == mainMenuCanvas);
        if (gameCanvas != null)
            gameCanvas.gameObject.SetActive(canvas == gameCanvas);
    }
    
    // Metódy pre testovanie
    public void TestGame()
    {
        Debug.Log("=== TEST HRY ===");
        Debug.Log("GameManager: " + (Instance != null ? "OK" : "CHYBA"));
        Debug.Log("AudioManager: " + (AudioManager.Instance != null ? "OK" : "CHYBA"));
        Debug.Log("Main Menu Canvas: " + (mainMenuCanvas != null ? "OK" : "CHYBA"));
        Debug.Log("Game Canvas: " + (gameCanvas != null ? "OK" : "CHYBA"));
        Debug.Log("=== KONIEC TESTU ===");
    }
    
    // Update pre klávesové skratky
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestGame();
        }
        
        if (Input.GetKeyDown(KeyCode.M))
        {
            ShowMainMenu();
        }
        
        if (Input.GetKeyDown(KeyCode.G))
        {
            StartNewGame();
        }
        
        if (Input.GetKeyDown(KeyCode.A))
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.PlayMenuMusic();
            }
        }
    }
}
