using UnityEngine;
using UnityEngine.UI;

public class QuickTest : MonoBehaviour
{
    [Header("Test UI")]
    public Button testButton;
    public Text statusText;
    
    void Start()
    {
        if (testButton != null)
        {
            testButton.onClick.AddListener(RunTest);
        }
        
        UpdateStatus("Hra pripravená na testovanie.");
    }
    
    public void RunTest()
    {
        UpdateStatus("Spúšťam test...");
        
        // Test GameManager
        GameManager gameManager = FindObjectOfType<GameManager>();
        if (gameManager != null)
        {
            UpdateStatus("✅ GameManager nájdený");
        }
        else
        {
            UpdateStatus("❌ GameManager nenájdený");
            return;
        }
        
        // Test AudioManager
        AudioManager audioManager = FindObjectOfType<AudioManager>();
        if (audioManager != null)
        {
            UpdateStatus("✅ AudioManager nájdený");
        }
        else
        {
            UpdateStatus("❌ AudioManager nenájdený");
        }
        
        // Test Canvas
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        UpdateStatus("✅ Nájdených " + canvases.Length + " Canvas objektov");

        // Test tlačidiel
        Button[] buttons = FindObjectsOfType<Button>();
        UpdateStatus("✅ Nájdených " + buttons.Length + " tlačidiel");
        
        UpdateStatus("Test dokončený! Hra je pripravená.");
    }
    
    void UpdateStatus(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
        Debug.Log("[QuickTest] " + message);
    }
    
    void Update()
    {
        // Rýchle klávesové skratky pre testovanie
        if (Input.GetKeyDown(KeyCode.T))
        {
            RunTest();
        }
        
        if (Input.GetKeyDown(KeyCode.M))
        {
            GameManager gameManager = FindObjectOfType<GameManager>();
            if (gameManager != null)
            {
                gameManager.ShowMainMenu();
                UpdateStatus("Zobrazené hlavné menu");
            }
        }
        
        if (Input.GetKeyDown(KeyCode.G))
        {
            GameManager gameManager = FindObjectOfType<GameManager>();
            if (gameManager != null)
            {
                gameManager.StartNewGame();
                UpdateStatus("Spustená nová hra");
            }
        }
        
        if (Input.GetKeyDown(KeyCode.A))
        {
            AudioManager audioManager = FindObjectOfType<AudioManager>();
            if (audioManager != null)
            {
                audioManager.PlayMenuMusic();
                UpdateStatus("Spustená menu hudba");
            }
        }
    }
}
