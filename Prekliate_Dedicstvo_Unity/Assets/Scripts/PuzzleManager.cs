using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class PuzzleManager : MonoBehaviour
{
    [Header("Gate Puzzle UI")]
    public Transform symbolsParent;
    public GameObject symbolButtonPrefab;
    public Transform sequenceParent;
    public GameObject sequenceSlotPrefab;
    public Button submitButton;
    public Button resetButton;
    public Button hintButton;
    public Text feedbackText;
    
    [Header("Puzzle Settings")]
    public Sprite crossSprite;
    public Sprite lionSprite;
    public Sprite eagleSprite;
    public Sprite heartSprite;
    
    private Dictionary<string, Sprite> symbolSprites = new Dictionary<string, Sprite>();
    private List<string> currentSequence = new List<string>();
    private string[] correctSolution = { "cross", "lion", "eagle", "heart" };
    private List<GameObject> sequenceSlots = new List<GameObject>();
    
    public static PuzzleManager Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        InitializeSymbols();
        SetupPuzzleUI();
    }
    
    void InitializeSymbols()
    {
        symbolSprites["cross"] = crossSprite;
        symbolSprites["lion"] = lionSprite;
        symbolSprites["eagle"] = eagleSprite;
        symbolSprites["heart"] = heartSprite;
    }
    
    void SetupPuzzleUI()
    {
        if (symbolsParent == null || symbolButtonPrefab == null) return;
        
        // Vytvoriť tlačidlá pre symboly
        string[] symbols = { "cross", "lion", "eagle", "heart" };
        
        foreach (string symbol in symbols)
        {
            GameObject buttonObj = Instantiate(symbolButtonPrefab, symbolsParent);
            Button button = buttonObj.GetComponent<Button>();
            Image buttonImage = buttonObj.GetComponent<Image>();
            
            if (symbolSprites.ContainsKey(symbol))
            {
                buttonImage.sprite = symbolSprites[symbol];
            }
            
            button.onClick.AddListener(() => AddSymbolToSequence(symbol));
        }
        
        // Vytvoriť sloty pre sekvenciu
        CreateSequenceSlots();
        
        // Nastaviť tlačidlá
        if (submitButton != null)
            submitButton.onClick.AddListener(CheckSolution);
        if (resetButton != null)
            resetButton.onClick.AddListener(ResetSequence);
        if (hintButton != null)
            hintButton.onClick.AddListener(ShowHint);
    }
    
    void CreateSequenceSlots()
    {
        if (sequenceParent == null || sequenceSlotPrefab == null) return;
        
        for (int i = 0; i < correctSolution.Length; i++)
        {
            GameObject slotObj = Instantiate(sequenceSlotPrefab, sequenceParent);
            sequenceSlots.Add(slotObj);
        }
    }
    
    public void AddSymbolToSequence(string symbol)
    {
        if (currentSequence.Count >= correctSolution.Length) return;
        
        currentSequence.Add(symbol);
        UpdateSequenceDisplay();
        
        // Zvuk kliknutia
        if (AudioManager.Instance != null)
        {
            // AudioManager.Instance.PlaySound("click");
        }
        
        // Ak je sekvencia kompletná, automaticky skontrolovať
        if (currentSequence.Count == correctSolution.Length)
        {
            StartCoroutine(DelayedCheck());
        }
    }
    
    IEnumerator DelayedCheck()
    {
        yield return new WaitForSeconds(0.5f);
        CheckSolution();
    }
    
    void UpdateSequenceDisplay()
    {
        for (int i = 0; i < sequenceSlots.Count; i++)
        {
            Image slotImage = sequenceSlots[i].GetComponent<Image>();
            
            if (i < currentSequence.Count)
            {
                string symbol = currentSequence[i];
                if (symbolSprites.ContainsKey(symbol))
                {
                    slotImage.sprite = symbolSprites[symbol];
                    slotImage.color = Color.white;
                }
            }
            else
            {
                slotImage.sprite = null;
                slotImage.color = Color.gray;
            }
        }
    }
    
    public void CheckSolution()
    {
        if (currentSequence.Count != correctSolution.Length)
        {
            ShowFeedback("Sekvencia nie je kompletná!", Color.yellow);
            return;
        }
        
        bool isCorrect = true;
        for (int i = 0; i < correctSolution.Length; i++)
        {
            if (currentSequence[i] != correctSolution[i])
            {
                isCorrect = false;
                break;
            }
        }
        
        if (isCorrect)
        {
            ShowFeedback("Správne! Brána sa otvára...", Color.green);
            StartCoroutine(SolvePuzzle());
        }
        else
        {
            ShowFeedback("Nesprávne. Skúste znovu.", Color.red);
            StartCoroutine(DelayedReset());
        }
    }
    
    IEnumerator SolvePuzzle()
    {
        yield return new WaitForSeconds(2f);
        
        // Puzzle vyriešené - pokračovať v hre
        if (GameManager.Instance != null)
        {
            GameManager.Instance.LoadScene("Nadvorie_Ticho");
        }
        
        // Zavrieť puzzle
        gameObject.SetActive(false);
    }
    
    IEnumerator DelayedReset()
    {
        yield return new WaitForSeconds(1.5f);
        ResetSequence();
    }
    
    public void ResetSequence()
    {
        currentSequence.Clear();
        UpdateSequenceDisplay();
        ShowFeedback("", Color.white);
    }
    
    public void ShowHint()
    {
        string hintText = "Nápoveda: Pozrite sa na erby na bráne. Poradie má význam - začnite s náboženským symbolom.";
        ShowFeedback(hintText, Color.cyan);
        
        StartCoroutine(ClearFeedbackAfterDelay(5f));
    }
    
    void ShowFeedback(string message, Color color)
    {
        if (feedbackText != null)
        {
            feedbackText.text = message;
            feedbackText.color = color;
        }
    }
    
    IEnumerator ClearFeedbackAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        ShowFeedback("", Color.white);
    }
    
    public void SetPuzzleSolution(string[] solution)
    {
        correctSolution = solution;
        ResetSequence();
    }
    
    public void ShowGatePuzzle()
    {
        gameObject.SetActive(true);
        ResetSequence();
        ShowFeedback("Vyberte správne poradie symbolov na otvorenie brány.", Color.white);
    }
    
    public void ClosePuzzle()
    {
        gameObject.SetActive(false);
    }
}
