using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System;

[System.Serializable]
public class GameSaveData
{
    public string currentChapter;
    public string currentScene;
    public List<string> sceneHistory;
    public List<JournalEntryData> journalEntries;
    public int currentJournalPage;
    public Dictionary<string, PuzzleStateData> puzzleStates;
    public float masterVolume;
    public float musicVolume;
    public float sfxVolume;
    public bool isMuted;
    public string saveDateTime;
    
    public GameSaveData()
    {
        sceneHistory = new List<string>();
        journalEntries = new List<JournalEntryData>();
        puzzleStates = new Dictionary<string, PuzzleStateData>();
        saveDateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }
}

[System.Serializable]
public class JournalEntryData
{
    public string title;
    public string content;
    public string timestamp;
    
    public JournalEntryData(string title, string content, string timestamp)
    {
        this.title = title;
        this.content = content;
        this.timestamp = timestamp;
    }
}

[System.Serializable]
public class PuzzleStateData
{
    public List<string> currentSequence;
    public bool solved;
    
    public PuzzleStateData()
    {
        currentSequence = new List<string>();
        solved = false;
    }
}

public class SaveSystem : MonoBehaviour
{
    private const string SAVE_FILE_NAME = "prekliatededicstvo_save.json";
    private string savePath;
    
    public static SaveSystem Instance { get; private set; }
    
    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            savePath = Path.Combine(Application.persistentDataPath, SAVE_FILE_NAME);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    public void SaveGame()
    {
        try
        {
            GameSaveData saveData = CreateSaveData();
            string jsonData = JsonUtility.ToJson(saveData, true);
            File.WriteAllText(savePath, jsonData);
            
            Debug.Log($"Hra uložená do: {savePath}");
        }
        catch (Exception e)
        {
            Debug.LogError("Chyba pri ukladaní hry: " + e.Message);
        }
    }
    
    public bool LoadGame()
    {
        try
        {
            if (!File.Exists(savePath))
            {
                Debug.Log("Uložená hra neexistuje.");
                return false;
            }
            
            string jsonData = File.ReadAllText(savePath);
            GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(jsonData);
            
            ApplySaveData(saveData);
            
            Debug.Log("Hra načítaná úspešne.");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError("Chyba pri načítavaní hry: " + e.Message);
            return false;
        }
    }
    
    public bool HasSaveFile()
    {
        return File.Exists(savePath);
    }
    
    public void DeleteSaveFile()
    {
        try
        {
            if (File.Exists(savePath))
            {
                File.Delete(savePath);
                Debug.Log("Uložená hra vymazaná.");
            }
        }
        catch (Exception e)
        {
            Debug.LogError("Chyba pri mazaní uloženej hry: " + e.Message);
        }
    }
    
    private GameSaveData CreateSaveData()
    {
        GameSaveData saveData = new GameSaveData();
        
        if (GameManager.Instance != null)
        {
            // Uložiť stav hry z GameManager
            // saveData.currentChapter = GameManager.Instance.currentChapter;
            // saveData.currentScene = GameManager.Instance.currentScene;
            // saveData.sceneHistory = new List<string>(GameManager.Instance.sceneHistory);
            // saveData.currentJournalPage = GameManager.Instance.currentJournalPage;
            
            // Konvertovať journal entries
            // foreach (var entry in GameManager.Instance.journalEntries)
            // {
            //     saveData.journalEntries.Add(new JournalEntryData(entry.title, entry.content, entry.timestamp));
            // }
        }
        
        if (AudioManager.Instance != null)
        {
            saveData.masterVolume = AudioManager.Instance.masterVolume;
            saveData.musicVolume = AudioManager.Instance.musicVolume;
            saveData.sfxVolume = AudioManager.Instance.sfxVolume;
            saveData.isMuted = AudioManager.Instance.isMuted;
        }
        
        // Uložiť stav puzzle
        // TODO: Implementovať ukladanie stavu puzzle
        
        return saveData;
    }
    
    private void ApplySaveData(GameSaveData saveData)
    {
        if (GameManager.Instance != null)
        {
            // Aplikovať stav hry
            // GameManager.Instance.currentChapter = saveData.currentChapter;
            // GameManager.Instance.currentScene = saveData.currentScene;
            // GameManager.Instance.sceneHistory = new List<string>(saveData.sceneHistory);
            // GameManager.Instance.currentJournalPage = saveData.currentJournalPage;
            
            // Obnoviť journal entries
            // GameManager.Instance.journalEntries.Clear();
            // foreach (var entryData in saveData.journalEntries)
            // {
            //     GameManager.Instance.journalEntries.Add(new GameManager.JournalEntry(entryData.title, entryData.content));
            // }
            
            // Načítať aktuálnu scénu
            if (!string.IsNullOrEmpty(saveData.currentChapter))
            {
                GameManager.Instance.LoadChapter(saveData.currentChapter);
            }
        }
        
        if (AudioManager.Instance != null)
        {
            AudioManager.Instance.SetMasterVolume(saveData.masterVolume);
            AudioManager.Instance.SetMusicVolume(saveData.musicVolume);
            AudioManager.Instance.SetSFXVolume(saveData.sfxVolume);
            AudioManager.Instance.isMuted = saveData.isMuted;
        }
        
        // Obnoviť stav puzzle
        // TODO: Implementovať obnovu stavu puzzle
    }
    
    public GameSaveData GetSaveData()
    {
        if (!File.Exists(savePath)) return null;
        
        try
        {
            string jsonData = File.ReadAllText(savePath);
            return JsonUtility.FromJson<GameSaveData>(jsonData);
        }
        catch (Exception e)
        {
            Debug.LogError("Chyba pri čítaní save dát: " + e.Message);
            return null;
        }
    }
    
    public string GetSaveDateTime()
    {
        GameSaveData saveData = GetSaveData();
        return saveData?.saveDateTime ?? "";
    }
}
