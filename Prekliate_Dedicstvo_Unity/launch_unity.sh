#!/bin/bash

echo "🚀 Spúšťam Unity s projektom Prekliate Dedičstvo..."

# Cesta k Unity
UNITY_PATH="/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/MacOS/Unity"

# Cesta k projektu
PROJECT_PATH="/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity"

# Skontrolovať, či Unity existuje
if [ ! -f "$UNITY_PATH" ]; then
    echo "❌ Unity nebolo nájdené na ceste: $UNITY_PATH"
    echo "💡 Skúste otvoriť Unity Hub a pridať projekt manuálne."
    exit 1
fi

# Skontrolovať, či projekt existuje
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ Projekt nebol nájdený na ceste: $PROJECT_PATH"
    exit 1
fi

echo "✅ Unity nájdené: $UNITY_PATH"
echo "✅ Projekt nájdený: $PROJECT_PATH"
echo ""
echo "🔄 Spúšťam Unity..."

# Spustiť Unity s projektom
"$UNITY_PATH" -projectPath "$PROJECT_PATH" &

echo "✅ Unity sa spúšťa na pozadí..."
echo ""
echo "📋 Ďalšie kroky:"
echo "1. Počkajte na otvorenie Unity Editor"
echo "2. Otvorte MainScene.unity"
echo "3. Stlačte Play ▶️"
echo "4. Testujte klávesy T, M, G, A"
echo ""
echo "🆘 Ak sa Unity neotvorí:"
echo "- Otvorte Unity Hub manuálne"
echo "- Kliknite 'Open' a vyberte priečinok projektu"
echo "- Alebo presuňte priečinok do Unity Hub"
