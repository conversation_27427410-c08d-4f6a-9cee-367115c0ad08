{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151], "DebugActionIndex": 0}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt", "DisplayName": "Writing Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 115, "PayloadLength": 86, "PayloadDebugContentSnippet": "/Users/<USER>/Desktop/Z", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 1}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Common.rsp", "ActionType": "WriteFile", "PayloadOffset": 302, "PayloadLength": 36267, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"], "OutputFlags": [2], "DebugActionIndex": 2}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2", "DisplayName": "Writing Unity.Multiplayer.Center.Common.rsp2", "ActionType": "WriteFile", "PayloadOffset": 36671, "PayloadLength": 99, "PayloadDebugContentSnippet": "/pathmap:\"/Users/<USER>", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"], "OutputFlags": [2], "DebugActionIndex": 3}, {"Annotation": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)", "DisplayName": "Compiling C# (Unity.Multiplayer.Center.Common)", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetCoreRuntime/dotnet\" exec \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp\" \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/AnswerData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSection.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSectionAnalyticsProvider.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/Preset.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/SelectedSolutionsData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/StyleConstants.cs", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.pdb", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [1, 2, 3, 127], "ToUseDependencies": [1, 3], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "CachingMode": "ByDirectInputs", "DebugActionIndex": 4}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 5}, {"Annotation": "ScriptAssemblies", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 132, 133, 134, 141, 144, 145, 146, 147, 148, 149], "DebugActionIndex": 6}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 7}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 8}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 9}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 10}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 11}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 12}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 13}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 14}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 15}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 16}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 17}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 18}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 19}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 20}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 21}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 22}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 23}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 24}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 25}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 26}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 27}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 28}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 29}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 30}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 31}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 32}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 33}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 34}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 35}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 36}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 37}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 38}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 39}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 40}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 41}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 42}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 43}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 44}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 45}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 46}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 47}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 48}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 49}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 50}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 51}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 52}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 53}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 54}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 55}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 56}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 57}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 58}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 59}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 60}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 61}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 62}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 63}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 64}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 65}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 66}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 67}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 68}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 69}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 70}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 71}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 72}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 73}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 74}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 75}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 76}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 77}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 78}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 79}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 80}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 81}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 82}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 83}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 84}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 85}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 86}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 87}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 88}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 89}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 90}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 91}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 92}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 93}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 94}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 95}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 96}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 97}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 98}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 99}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 100}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 101}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 102}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 103}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 104}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 105}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 106}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 107}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 108}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 109}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 110}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 111}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 112}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 113}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 114}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 115}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 116}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 117}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 118}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 119}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 120}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 121}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 122}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 123}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 124}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 125}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Common.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 36881, "PayloadLength": 10281, "PayloadDebugContentSnippet": "Library/Bee/artifacts/mvdfrm/U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 126}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm\" \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126], "AllowUnexpectedOutput": true, "DebugActionIndex": 127}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 47283, "PayloadLength": 86, "PayloadDebugContentSnippet": "/Users/<USER>/Desktop/Z", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 128}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.rsp", "ActionType": "WriteFile", "PayloadOffset": 47470, "PayloadLength": 41504, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"], "OutputFlags": [2], "DebugActionIndex": 129}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.rsp2", "ActionType": "WriteFile", "PayloadOffset": 89076, "PayloadLength": 99, "PayloadDebugContentSnippet": "/pathmap:\"/Users/<USER>", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"], "OutputFlags": [2], "DebugActionIndex": 130}, {"Annotation": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)", "DisplayName": "Compiling C# (Unity.Multiplayer.Center.Editor)", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetCoreRuntime/dotnet\" exec \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp\" \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/DebugAnalytics.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalytics.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalyticsFactory.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/OnboardingSectionAnalyticsProvider.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/AssemblyInfo.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Features/PackageManagement.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/MultiplayerCenterWindow.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationTabView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationViewBottomBar.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/TabGroup.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionnaireView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionSection.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionViewFactory.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/PackageSelectionView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationItemView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SectionHeader.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SolutionSelectionView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/StyleClasses.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/ViewUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/GettingStartedTabView.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/QuickstartPackageHandling.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/SectionsFinder.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/Logic.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/PresetData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireEditor.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/UserChoicesObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/PreReleaseHandling.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationAuthoringData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationType.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationUtils.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationViewData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystem.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemData.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemDataObject.cs", "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/Scoring.cs", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.pdb", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [4, 128, 129, 130, 136], "ToUseDependencies": [128, 130], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "CachingMode": "ByDirectInputs", "DebugActionIndex": 131}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm\" \"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll\"", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [4], "AllowUnexpectedOutput": true, "DebugActionIndex": 132}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_635F19CBEFBD7AFC.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_635F19CBEFBD7AFC.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_635F19CBEFBD7AFC.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 133}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_58EFA0CF97C8FA6B.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_58EFA0CF97C8FA6B.mvfrm\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_58EFA0CF97C8FA6B.mvfrm"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "DebugActionIndex": 134}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp", "DisplayName": "Writing Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 89286, "PayloadLength": 10532, "PayloadDebugContentSnippet": "Library/Bee/artifacts/mvdfrm/U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 135}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm\" \"@Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_635F19CBEFBD7AFC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_58EFA0CF97C8FA6B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 132, 133, 134, 135], "AllowUnexpectedOutput": true, "DebugActionIndex": 136}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt", "DisplayName": "Writing Assembly-CSharp.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 99923, "PayloadLength": 86, "PayloadDebugContentSnippet": "/Users/<USER>/Desktop/Z", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"], "OutputFlags": [2], "DebugActionIndex": 137}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp", "DisplayName": "Writing Assembly-CSharp.rsp", "ActionType": "WriteFile", "PayloadOffset": 100094, "PayloadLength": 35778, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"], "OutputFlags": [2], "DebugActionIndex": 138}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2", "DisplayName": "Writing Assembly-CSharp.rsp2", "ActionType": "WriteFile", "PayloadOffset": 135958, "PayloadLength": 0, "PayloadDebugContentSnippet": "", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2"], "OutputFlags": [2], "DebugActionIndex": 139}, {"Annotation": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)", "DisplayName": "Compiling C# (Assembly-CSharp)", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetCoreRuntime/dotnet\" exec \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp\" \"@Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll", "Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll", "Assets/Scripts/AudioManager.cs", "Assets/Scripts/AutoSetup.cs", "Assets/Scripts/GameData.cs", "Assets/Scripts/GameManager.cs", "Assets/Scripts/ImageLoader.cs", "Assets/Scripts/PuzzleManager.cs", "Assets/Scripts/QuickTest.cs", "Assets/Scripts/SaveSystem.cs", "Assets/Scripts/SimpleGameManager.cs", "Assets/Scripts/UIEffects.cs", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll", "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll", "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.pdb", "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"], "OutputFlags": [0, 0, 0], "ToBuildDependencies": [4, 131, 137, 138, 139, 143], "ToUseDependencies": [137, 139], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "DebugActionIndex": 140}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm\" \"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll\"", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll"], "InputFlags": [0, 0], "Outputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [131], "AllowUnexpectedOutput": true, "DebugActionIndex": 141}, {"Annotation": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp", "DisplayName": "Writing Assembly-CSharp.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 136053, "PayloadLength": 10212, "PayloadDebugContentSnippet": "Library/Bee/artifacts/mvdfrm/U", "Inputs": [], "InputFlags": [], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"], "OutputFlags": [2], "DebugActionIndex": 142}, {"Annotation": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm", "DisplayName": "Checking for moved APIs", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll\" \"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm\" \"@Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm", "Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"], "OutputFlags": [0], "ToBuildDependencies": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 132, 141, 142], "AllowUnexpectedOutput": true, "DebugActionIndex": 143}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "DisplayName": "Copying Unity.Multiplayer.Center.Common.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 144}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb", "DisplayName": "Copying Unity.Multiplayer.Center.Common.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"], "OutputFlags": [2], "ToBuildDependencies": [4], "DebugActionIndex": 145}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll", "DisplayName": "Copying Unity.Multiplayer.Center.Editor.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"], "OutputFlags": [2], "ToBuildDependencies": [131], "DebugActionIndex": 146}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb", "DisplayName": "Copying Unity.Multiplayer.Center.Editor.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"], "OutputFlags": [2], "ToBuildDependencies": [131], "DebugActionIndex": 147}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll", "DisplayName": "Copying Assembly-CSharp.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Assembly-CSharp.dll"], "OutputFlags": [2], "ToBuildDependencies": [140], "DebugActionIndex": 148}, {"Annotation": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb", "DisplayName": "Copying Assembly-CSharp.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.pdb"], "InputFlags": [0], "Outputs": ["Library/ScriptAssemblies/Assembly-CSharp.pdb"], "OutputFlags": [2], "ToBuildDependencies": [140], "DebugActionIndex": 149}, {"Annotation": "BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json", "DisplayName": "Extracting script serialization layouts", "Action": "\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun\" \"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe\" -a=\"/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity/Library/ScriptAssemblies/Assembly-CSharp.dll\" -a=\"/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity/Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/EditorExtensions\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/Extensions/2.0.0\" -s=\"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/ref/2.1.0\" -s=\"/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity/Library/ScriptAssemblies\" -o=\"Library/BuildPlayerData/Editor\" -rn=\"\" -tn=\"TypeDb-All.json\"", "Inputs": ["/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/netcorerun/netcorerun", "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"], "InputFlags": [0, 0, 0, 0], "Outputs": ["Library/BuildPlayerData/Editor/TypeDb-All.json"], "OutputFlags": [0], "ToBuildDependencies": [144, 148], "AllowUnexpectedOutput": true, "DebugActionIndex": 150}, {"Annotation": "ScriptAssembliesAndTypeDB", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [6, 150], "DebugActionIndex": 151}], "FileSignatures": [{"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Stevedore.Program.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/NiceIO.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline/UnityEditor.iOS.Extensions.Xcode.dll"}, {"File": "Library/Bee/200b0aE-inputdata.json"}], "StatSignatures": [{"File": "/Users/<USER>/Desktop/ZVUKY/<PERSON>bicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity/getconf"}, {"File": "Assets/csc.rsp"}, {"File": "Assets/mcs.rsp"}, {"File": "Library/Bee/200b0aE-inputdata.json"}, {"File": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/csc.rsp"}, {"File": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/csc.rsp"}], "GlobSignatures": [{"Path": "/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/BuildPipeline"}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".pdb", ".json", ".c<PERSON><PERSON>j"], "StructuredLogFileName": "Library/Bee/tundra.log.json", "StateFileName": "Library/Bee/TundraBuildState.state", "StateFileNameTmp": "Library/Bee/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/Bee/TundraBuildState.state.map", "ScanCacheFileName": "Library/Bee/tundra.scancache", "ScanCacheFileNameTmp": "Library/Bee/tundra.scancache.tmp", "DigestCacheFileName": "Library/Bee/tundra.digestcache", "DigestCacheFileNameTmp": "Library/Bee/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/Bee/CachedNodeOutput", "EmitDataForBeeWhy": 0, "NamedNodes": {"all_tundra_nodes": 0, "ScriptAssemblies": 6, "ScriptAssembliesAndTypeDB": 151}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/Bee/200b0aE.dag.json", "PayloadsFile": "/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity/Library/Bee/200b0aE.dag.payloads", "RelativePathToRoot": "../.."}