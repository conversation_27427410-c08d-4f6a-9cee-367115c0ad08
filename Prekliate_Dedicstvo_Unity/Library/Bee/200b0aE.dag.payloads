Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-define:UNITY_6000_1_5
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/AnswerData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSection.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/IOnboardingSectionAnalyticsProvider.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/Preset.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/SelectedSolutionsData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Common/StyleConstants.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"

/pathmap:"/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity"=.    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-define:UNITY_6000_1_5
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/AnalyticsUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/DebugAnalytics.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalytics.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/MultiplayerCenterAnalyticsFactory.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Analytics/OnboardingSectionAnalyticsProvider.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/AssemblyInfo.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Features/PackageManagement.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/MultiplayerCenterWindow.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationTabView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/RecommendationViewBottomBar.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/TabGroup.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionnaireView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionSection.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/QuestionViewFactory.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/PackageSelectionView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationItemView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/RecommendationView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SectionHeader.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/RecommendationView/SolutionSelectionView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/StyleClasses.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/MultiplayerCenterWindow/UI/ViewUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/GettingStartedTabView.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/QuickstartPackageHandling.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/OnBoarding/SectionsFinder.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/Logic.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/PresetData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireEditor.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/QuestionnaireObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Questionnaire/UserChoicesObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/PreReleaseHandling.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationAuthoringData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationType.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationUtils.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommendationViewData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystem.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemData.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/RecommenderSystemDataObject.cs"
"Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546/Editor/Recommendations/Scoring.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"

/pathmap:"/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity"=.    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_751EC72B06DA30CC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_2B9930766D9FA726.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_635F19CBEFBD7AFC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_58EFA0CF97C8FA6B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_24A571090DE2A397.mvfrm    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"

/Users/<USER>/Desktop/ZVUKY/Kubicek/Carovny les_FINAL/Prekliate_Dedicstvo_Unity    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp"

-target:library
-out:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_1_5
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_OSX
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_OSX
-define:UNITY_STANDALONE
-define:ENABLE_GAMECENTER
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:ENABLE_SPATIALTRACKING
-define:PLATFORM_HAS_CUSTOM_MUTEX
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"/Applications/Unity/Hub/Editor/6000.1.5f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Scripts/AudioManager.cs"
"Assets/Scripts/AutoSetup.cs"
"Assets/Scripts/GameData.cs"
"Assets/Scripts/GameManager.cs"
"Assets/Scripts/ImageLoader.cs"
"Assets/Scripts/PuzzleManager.cs"
"Assets/Scripts/QuickTest.cs"
"Assets/Scripts/SaveSystem.cs"
"Assets/Scripts/SimpleGameManager.cs"
"Assets/Scripts/UIEffects.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.rsp2"

    

Payload for "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"

Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm
Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_4AA61037746A57ED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_265C2F443A4A6199.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_D45FEEDD7F2C5490.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_A64728BE60919093.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A41B5623131710F4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_A3EC7B69A8363B0D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0D2196B8E6C1621B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.dll_6EB925FB9E614661.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_80A22609EE00093D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_00DE88E83A2BDFBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_62EE9B052EDBFD17.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5AD8C702E4891A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_7F1DFF6A5A6AD092.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_16DA7D3A6A261F0A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_818C72112B6BA6EB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_55FC97B5488C9059.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_E4637A9AF044EAE8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8DA50EEF5E16C4F6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_3A84384C5CC32897.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_073B7B5D3C0DAE68.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_983117C63F985E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_63CDACF71BBC8275.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_A44196C7ECECB375.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_BDCDE2724F8D9DA0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_E321670B0F9C3003.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_4BE2924766EE1E8C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C32CAEAEEA91DFA6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EFB5A88345673AEB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_28FD8C43EA22A89F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A7EB8A5E33D50DE4.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_21E9C5FFBC1A3FF2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_69D1FC714B9DF910.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_EDE4B9033F4964CE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_46E7F1DF4B91EEED.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_01C74B319ADCA633.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_8500DDCE1BA01D50.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_D3A6AC86CD2349C6.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_32187678BC0603B0.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_8FD7F0DAD332E8BD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1593033938A72F8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_CF99FC53A41039AA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_D6ABD34C7DAB51B7.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4B8E0885C8F5E32A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4BB6D9AF1A68ED7F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D6E7025EED62DB12.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_0920E386FEAA48C8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B0615F4C05257286.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_4DA37B9334C7A538.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C293A88A5BC7A791.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DC1A4AB7A1FF1366.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ECB1407F37E0F82A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_53999D7A11FDD70E.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_4F36716955980C70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_A9993DDB17F14891.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7530A3F87081BB53.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C73C2975BB1C7C1F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_044B864216B05917.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_5EE46F779F8759FC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A15D6CD3FCD4D346.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_6435D0E111E82B1D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DAAEA966CB666377.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A15D2C949A5930E2.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_63CC93F2B18863BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A4AD4563D3711216.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_4E019F395C103408.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_CEEC39990FFAC0BA.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_158B3B8B9A160085.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_2510DFE5FE128C0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_DD6103802B870033.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6D5F65D639F0C957.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_5327082FAE191CCE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_36FE8B9AEC9E3B9C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_8D833BBBED13FF69.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8482E94C862740B8.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_5BC267445AC6A0DE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_575DCF8E84C1E4A5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_8A3045CAD169EFDE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_9A496B83D5C49CCD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_801FC799C325AC3B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_07E9E8A53E51C7B3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_C1B10A64BDF32796.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_50E63920010A67FE.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_6F19B673B0FD1686.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_999C7A611143FBD5.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C3DB1BF6ECD5F8F1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F57B781257A14B70.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_023868E3844F5C76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_DEE37D2CCFA25A84.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2E888258BB1DE104.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9A66430898E3F529.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_57D878619E04DF32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A0494152CF17FEBD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_E83177C45E9BB7DB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_7A25AB2AB841E7F3.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3962AA89B0A0D632.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_61EEDD3116EDF64F.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_32C6E3C0BE4A1CFB.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_049481B45AF14E74.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_6785EC97C507A39D.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_256F030EEB528E3C.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_76FD405830E936A1.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_BCF980629E983A55.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_80C01CC4A4DB9725.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4B9AA6CD2D2E3E13.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_3B5177937403EA32.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_022C99A957D4BA76.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_272A4335A50A9B04.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_AE5F98C30530DB0B.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B2F3BD204E5B7447.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_D6A768F719B12997.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E2CD759FED5B5FBC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_11BAC4E9FA7313CF.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_568B7281BAE0C5FD.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_5766A839FCABD3EC.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3D8B049E4C52FA88.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8A256B3E85E6214A.mvfrm
Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_355D7C746579412A.mvfrm    

