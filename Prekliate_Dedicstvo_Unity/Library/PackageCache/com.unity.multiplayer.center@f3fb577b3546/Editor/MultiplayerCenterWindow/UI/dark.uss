/*
 * CSS variables specific to dark mode (pro skin)
 */
:root {
    --package-icon: url('Icons/d_Package.png');
    --questionnaire-icon: url('Icons/d_Questionnaire.png');
    --package-manager-icon: url('Icons/d_PackageManager.png');
    --package-installed-icon: url('Icons/d_PackageInstalled.png');
    --info-icon: resource('d__Help');
    --comment-color: dimgrey;
    --highlight-background-color: #2a2a2a;
    --colors-incompatible-background: #FFC107;
    --recommendation-badge-color: #69E39F;
    --card-poster-image-bg-color: #222222;
    --theme-slider-background-color:#5E5E5E; /* From Default common dark uss */
    --badge-color-grey:#C4C4C4; /* From Default common dark uss, feedback color */
    --pre-release-badge-color: #FFC107;
    --pre-release-badge-color-bg: #1D1E1F;
    --link-color: var(--unity-colors-label-text-focus);
    --tab-button-highlight-color: #DEDEDE;
    --onboarding-button-selected-text-color: #EEEEEE;
    --three-dot-icon: resource("UIBuilderPackageResources/Icons/Dark/Inspector/Status/Settings.png");
    --spinner-icon-big: url('Icons/d_Loading.png');
}