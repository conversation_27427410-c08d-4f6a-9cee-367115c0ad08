# Prekliate Dedičstvo - Unity Verzia

Toto je Unity konverzia webovej hry "Prekliate Dedičstvo" - gotickej vizuálnej novely s prvkami adventure hry.

## Popis Hry

**Prekliate Dedičstvo** je interaktívna vizuálna novela zasadená do gotického sveta Van Helsinga. Hráč sa ocitá pred z<PERSON>hadným zámkom, kde musí riešiť puzzle a odkrývať tajomstvá.

### Hlavné Funkcie:
- **Kapitoly**: Postupné odhaľovanie príbehu cez jednotlivé kapitoly
- **Puzzle systém**: Interaktív<PERSON> h<PERSON> (napr. brána s erbmi)
- **Denník**: Záznam dôležitých udalostí a pozorovaní
- **Audio systém**: Atmosférická hudba a zvukové efekty
- **Save/Load**: Ukladanie a načítavanie postupu hry

## Technické Detaily

### Štruktúra Projektu:
```
Assets/
├── Scripts/           # C# skripty
│   ├── GameManager.cs      # Hlavný manažér hry
│   ├── AudioManager.cs     # Správa zvukov a hudby
│   ├── GameData.cs         # Herné dáta a konfigurácia
│   ├── PuzzleManager.cs    # Systém puzzle
│   ├── ImageLoader.cs      # Načítavanie obrázkov z URL
│   ├── UIEffects.cs        # UI animácie a efekty
│   └── SaveSystem.cs       # Ukladanie/načítavanie hry
├── Scenes/            # Unity scény
├── Images/            # Lokálne obrázky
├── Audio/             # Audio súbory
└── Data/              # Konfiguračné súbory
```

### Hlavné Komponenty:

#### GameManager
- Riadenie celkového toku hry
- Prepínanie medzi menu, kapitolami a scénami
- Správa denníka a histórie scén

#### AudioManager
- Prehrávanie hudby a zvukových efektov
- Podpora pre načítavanie audio z URL
- Ovládanie hlasitosti a stíšenia

#### PuzzleManager
- Systém pre interaktívne puzzle
- Brána s erbmi (cross, lion, eagle, heart)
- Kontrola riešení a feedback

#### ImageLoader
- Asynchrónne načítavanie obrázkov z internetu
- Fade-in efekty pre plynulé zobrazenie
- Cache systém pre optimalizáciu

## Nastavenie Projektu

### Požiadavky:
- Unity 2021.3 LTS alebo novší
- Internetové pripojenie (pre načítavanie obrázkov a zvukov)

### Kroky na spustenie:

1. **Otvorenie projektu**:
   - Otvorte Unity Hub
   - Kliknite na "Open" a vyberte priečinok `Prekliate_Dedicstvo_Unity`

2. **Nastavenie scény**:
   - Vytvorte novú scénu: `File > New Scene`
   - Uložte ju ako `MainScene.unity` v priečinku `Assets/Scenes/`

3. **Vytvorenie UI**:
   - Pridajte Canvas: `GameObject > UI > Canvas`
   - Nastavte Canvas Scaler na "Scale With Screen Size"
   - Reference Resolution: 1080x1920 (mobilné rozlíšenie)

4. **Pridanie manažérov**:
   - Vytvorte prázdny GameObject s názvom "GameManager"
   - Pripojte skript `GameManager.cs`
   - Vytvorte ďalšie GameObjecty pre AudioManager, PuzzleManager, atď.

### Mobilné Nastavenia:

Projekt je optimalizovaný pre mobilné zariadenia:
- **Orientácia**: Portrait (na výšku)
- **Rozlíšenie**: 1080x1920
- **Target SDK**: Android 22+, iOS 11+
- **UI**: Responzívny dizajn s Canvas Scaler

## Herné Mechaniky

### Kapitoly:
1. **Kapitola 2: Nádvorie** - Príchod k bráne zámku
2. **Kapitola 3-6**: Pripravené na budúce rozšírenie
3. **Epilóg**: Záverečná kapitola

### Puzzle - Brána Zámku:
- **Cieľ**: Nájsť správne poradie symbolov
- **Riešenie**: cross → lion → eagle → heart
- **Mechanika**: Klikanie na symboly, automatická kontrola

### Denník:
- Automatické zaznamenávanie dôležitých udalostí
- Navigácia medzi stránkami
- Časové značky pre každý záznam

## Rozšírenia a Úpravy

### Pridanie novej kapitoly:
1. Upravte `GameData.cs` - pridajte novú kapitolu do zoznamu
2. Vytvorte nové scény v `GameManager.cs`
3. Pridajte obrázky a audio súbory

### Nové puzzle:
1. Rozšírte `PuzzleManager.cs`
2. Vytvorte nové UI komponenty
3. Definujte riešenie v `GameData.cs`

### Lokalizácia:
- Všetky texty sú v slovenčine
- Pre pridanie ďalších jazykov vytvorte lokalizačný systém

## Známe Problémy a Riešenia

### Načítavanie z URL:
- Niektoré obrázky môžu mať CORS obmedzenia
- Riešenie: Použite lokálne kópie obrázkov

### Audio na mobilných zariadeniach:
- iOS vyžaduje používateľskú interakciu pred prehrávaním
- Riešenie: Implementované v AudioManager.ResumeContext()

### Performance:
- Veľké obrázky môžu spomaľovať hru
- Riešenie: Komprimujte textúry v Unity Import Settings

## Budúce Vylepšenia

- [ ] Kompletné kapitoly 3-6
- [ ] Viac typov puzzle
- [ ] Animácie postáv
- [ ] Rozšírený save systém
- [ ] Achievements systém
- [ ] Nastavenia grafiky

## Kontakt

Pre otázky a podporu kontaktujte vývojára.

---

*Vytvorené s Unity 2021.3 LTS*
*Konvertované z webovej verzie (Rosebud AI)*
